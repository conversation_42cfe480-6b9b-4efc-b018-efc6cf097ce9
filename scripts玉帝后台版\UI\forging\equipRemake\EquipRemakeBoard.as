package UI.forging.equipRemake
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import UI.forging.equipUpgrade.EquipUpgradeBoard;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.EquipRemakeCtrl;
   import dataAll.equip.creator.EquipSkillCreator;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.creator.OneProData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.SkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EquipRemakeBoard extends NormalUI
   {
      
      private var copyData:EquipData;
      
      public var equipUpgradeBoard:EquipUpgradeBoard;
      
      private var proTag:Sprite;
      
      private var mustSp:Sprite;
      
      private var itemsGripSp:MovieClip;
      
      private var btnSp:MovieClip;
      
      private var gotoBackSp:MovieClip;
      
      private var proTxt:TextField;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var gotoBackBtn:NormalBtn = new NormalBtn();
      
      private var itemsGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var proBox:ItemsGripBox = new ItemsGripBox();
      
      private var _nowData:EquipData;
      
      private var nowProDataArr:Array = [];
      
      private var tempRemakeSave:EquipSave = null;
      
      private var beforeSave:EquipSave = null;
      
      public function EquipRemakeBoard()
      {
         super();
      }
      
      public function set nowData(da0:EquipData) : void
      {
         if(da0 != this._nowData || !da0)
         {
            this.nowProDataArr = [];
            this.beforeSave = null;
         }
         this.equipUpgradeBoard.nowData = da0;
         this._nowData = da0;
      }
      
      public function get nowData() : EquipData
      {
         return this.equipUpgradeBoard.nowData;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["proTag","mustSp","btnSp","gotoBackSp","itemsGripSp","proTxt"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("");
         addChild(this.gotoBackBtn);
         this.gotoBackBtn.setImg(this.gotoBackSp);
         this.gotoBackBtn.setName("编辑当前数据");
         addChild(this.itemsGrip);
         this.itemsGrip.setImg(this.itemsGripSp);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.itemsGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.proBox);
         NormalUICtrl.setTag(this.proBox,this.proTag);
         this.proBox.arg.init(1,8,0,4);
         this.proBox.evt.setWantEvent(true,false,false,true,true);
         this.proBox.setIconPro("ForgingUI/armsProBar",50,50);
         this.proBox.addEventListener(ClickEvent.ON_CLICK,this.proBarClick);
         this.proBox.addEventListener(ClickEvent.ON_OVER,this.proBarOver);
         this.proBox.addEventListener(ClickEvent.ON_OUT,this.proBarOut);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.btn.addEventListener(MouseEvent.MOUSE_OUT,this.proBarOut);
         this.gotoBackBtn.addEventListener(MouseEvent.CLICK,this.gotoBackBtnClick);
         this.gotoBackBtn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.gotoBackBtn.addEventListener(MouseEvent.MOUSE_OUT,this.proBarOut);
         this.showNone();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"equip");
         this.showOneEquipDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:EquipData) : void
      {
         if(visible)
         {
            this.showOneEquipDataAndPan(da0);
         }
      }
      
      private function proBarClick(e:ClickEvent) : void
      {
         if(this.proBox.gripArr.length == 1)
         {
            return;
         }
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var da0:OneProData = e.childData as OneProData;
         if(!da0.noChangeLockB)
         {
            da0.lockB = !da0.lockB;
         }
         grip0.inData_proData(da0);
         this.fleshMust();
      }
      
      private function proBarOver(e:ClickEvent) : void
      {
         var d0:SkillDefine = null;
         var str0:String = null;
         this.proBarOut(e);
         var da0:OneProData = e.childData as OneProData;
         if(da0.type == "skill")
         {
            d0 = Gaming.defineGroup.skill.getDefine(da0.name);
            str0 = ComMethod.color("<b>" + d0.cnName + "</b>","#FFFF00");
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0 + "\n" + d0.getDescription());
         }
      }
      
      private function proBarOut(e:Event) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function btnOver(e:Event) : void
      {
         var str0:String = "";
         if(e.target != this.btn)
         {
            if(e.target == this.gotoBackBtn)
            {
               str0 = "除了装备时装外其他equip不得编辑！";
            }
         }
         if(str0 == "")
         {
            this.proBarOut(e);
         }
         else
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function showOneEquipDataAndPan(da0:EquipData, fleshNowProDataArrB0:Boolean = false) : void
      {
         var dg0:EquipDataGroup = null;
         this.btn.setName("复制装备");
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要复制的装备。");
         if(this.copyData != null)
         {
            this.btn.actived = true;
            this.btn.setName("添加装备");
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
         if(da0)
         {
            dg0 = Gaming.PG.da.findEquipData(da0,false);
            if(dg0 is EquipDataGroup)
            {
               this.showOneEquipData(da0,fleshNowProDataArrB0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneEquipData(da0:EquipData, fleshNowProDataArrB0:Boolean = false) : void
      {
         this.nowData = da0;
         this.itemsGrip.inData_equip(da0);
         if(fleshNowProDataArrB0 || this.nowProDataArr.length == 0)
         {
            this.nowProDataArr = EquipSkillCreator.getOneProDataArr(da0.save);
         }
         this.proBox.inData_byArr(this.nowProDataArr,"inData_proData");
         if(this.nowProDataArr.length == 1)
         {
            this.proBox.setAllFun("setShopBtnBackMc","");
         }
         this.fleshMust();
      }
      
      private function fleshMust() : void
      {
         var s0:EquipSave = this.nowData.save;
         var must_d0:MustDefine = EquipRemakeCtrl.getMust(this.nowData.save,this.nowProDataArr);
         var mustB0:Boolean = this.mustBox.inData(must_d0);
         var lockAllB0:Boolean = this.proIsLockAllB();
         var colorB0:Boolean = EquipColor.firstMax(this.nowData.getColor(),"orange",true);
         this.btn.actived = true;
         this.gotoBackBtn.visible = true;
         this.proTxt.text = "";
         if(!colorB0)
         {
            this.proTxt.htmlText = ComMethod.color("点击复制装备即可复制当前装备","#FF0000");
         }
         else if(this.nowProDataArr.length == 0)
         {
            this.proTxt.text = "";
         }
      }
      
      private function proIsLockAllB() : Boolean
      {
         var da0:OneProData = null;
         if(this.nowProDataArr.length == 0)
         {
            return false;
         }
         for each(da0 in this.nowProDataArr)
         {
            if(!da0.lockB)
            {
               return false;
            }
         }
         return true;
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.beforeSave = null;
         this.nowProDataArr = [];
         this.tempRemakeSave = null;
         this.itemsGrip.clearData();
         this.proBox.inData_byArr([],"inData_proData");
         this.mustBox.setShowState(false);
         this.btn.actived = true;
         this.gotoBackBtn.visible = true;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var s0:EquipSave = new EquipSave();
         if(this.copyData == null)
         {
            this.copyData = this.nowData;
            Gaming.uiGroup.alertBox.showSuccess("复制当前装备数据成功！");
            this.showOneEquipDataAndPan(this.nowData);
         }
         else
         {
            s0.inData_byObj(this.copyData.save);
            Gaming.PG.da.equipBag.addSave(s0);
            Gaming.uiGroup.alertBox.showSuccess("添加装备成功！");
            this.copyData = null;
            this.showOneEquipDataAndPan(this.nowData);
         }
      }
      
      private function gotoBackBtnClick(e:MouseEvent) : void
      {
         var s0:EquipSave = new EquipSave();
         s0.inData_byObj(this.nowData.save);
         if(this.nowData)
         {
            if(s0.partType == "fashion" || s0.partType == "coat" || s0.partType == "pants" || s0.partType == "head" || s0.partType == "belt")
            {
               this.showEquipEditMenu();
            }
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("装备数据不存在！");
         }
      }
      
      private function EquipEdit_Equip(str0:String) : void
      {
         var i:int = 0;
         var j:int = 0;
         var ArrColor0:Array = ["white","green","blue","purple","orange","red","black","darkgold","purgold","yagold"];
         var ArrColor1:Array = ["白","绿","蓝","紫","橙","红","黑","暗金","紫金","氩星"];
         var Arr_type0:Array = ["coat","pants","head","belt","fashion"];
         var Arr_type1:Array = ["衣服","裤子","头盔","腰带","时装"];
         var s0:EquipSave = new EquipSave();
         var ArrNum:Array = new Array();
         var ArrNow:Array = new Array();
         var EquipNow:String = null;
         ArrNum = str0.split("&",str0.length);
         s0.inData_byObj(this.nowData.save);
         for each(EquipNow in ArrNum)
         {
            ArrNow = EquipNow.split("*",EquipNow.length);
            if(ArrNow[0] == "00")
            {
               s0.addLevel = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "01")
            {
               i = 0;
               while(i < ArrColor0.length)
               {
                  if(ArrNow[1] == ArrColor1[i])
                  {
                     s0.color = ArrColor0[i];
                  }
                  i++;
               }
            }
            else if(ArrNow[0] == "02")
            {
               s0.strengthenLv = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "03")
            {
               s0.evoLv = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "04")
            {
               j = 0;
               while(j < Arr_type0.length)
               {
                  if(ArrNow[1] == Arr_type1[j])
                  {
                     s0.partType = Arr_type0[j];
                  }
                  j++;
               }
            }
            else if(ArrNow[0] == "05")
            {
               s0.itemsLevel = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "06")
            {
               s0.getTime = ArrNow[1];
            }
            else if(ArrNow[0] == "07")
            {
               s0.severTime = ArrNow[1];
            }
            else if(ArrNow[0] == "08")
            {
               s0.severTime = "9999-9-9 12:00:00";
            }
            else if(ArrNow[0] == "09")
            {
               if(ArrNow[1] == "所有" || ArrNow[1] == "all")
               {
                  // 添加所有装备技能
                  var allSkillArr:Array = this.getAllEquipSkills(s0.partType);
                  s0.skillArr = s0.skillArr.concat(allSkillArr);
                  Gaming.uiGroup.alertBox.showSuccess("已添加所有装备技能，共" + allSkillArr.length + "个技能！");
               }
               else
               {
                  s0.skillArr.push(ArrNow[1]);
               }
            }
            else if(ArrNow[0] == "10")
            {
               s0.skillArr.splice(s0.skillArr.indexOf(ArrNow[1]),1);
            }
            else if(ArrNow[0] == "11")
            {
               this.copyData = null;
               Gaming.uiGroup.alertBox.showSuccess("copyData = null");
            }
            // 扩展属性编辑功能 - 特殊掉落和获取属性
            else if(ArrNow[0] == "12" || ArrNow[0] == "无双水晶掉落")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["demStroneDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置无双水晶掉落数量为：" + ArrNow[1]);
            }
            else if(ArrNow[0] == "13" || ArrNow[0] == "万能球掉落")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["demBallDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置万能球掉落数量为：" + ArrNow[1]);
            }
            else if(ArrNow[0] == "14" || ArrNow[0] == "战神之心掉落")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["madheartDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置战神之心掉落数量为：" + ArrNow[1]);
            }
            else if(ArrNow[0] == "15" || ArrNow[0] == "幸运值")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["lottery"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置幸运值为：" + ArrNow[1]);
            }
            else if(ArrNow[0] == "16" || ArrNow[0] == "商运掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["coinMul"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置商运掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "17" || ArrNow[0] == "优胜券获取")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["arenaStampDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置优胜券获取数量为：" + ArrNow[1]);
            }
            else if(ArrNow[0] == "18" || ArrNow[0] == "载具碎片掉落")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["vehicleCashDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置载具碎片掉落数量为：" + ArrNow[1]);
            }
            else if(ArrNow[0] == "19" || ArrNow[0] == "生命催化剂掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["lifeCatalystDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置生命催化剂掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "20" || ArrNow[0] == "神能石掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["godStoneDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置神能石掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "21" || ArrNow[0] == "转化石掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["converStoneDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置转化石掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "22" || ArrNow[0] == "化石掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["taxStampDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置化石掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "23" || ArrNow[0] == "血手掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["bloodStoneDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置血手掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "24" || ArrNow[0] == "装置掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["deviceDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置装置掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "25" || ArrNow[0] == "赠礼好感度")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["loveAdd"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置赠礼好感度为：" + ArrNow[1] + "点");
            }
            else if(ArrNow[0] == "26" || ArrNow[0] == "好感度每天")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["dayLoveAdd"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置每日好感度为：" + ArrNow[1] + "点");
            }
            else if(ArrNow[0] == "27" || ArrNow[0] == "战斗力神级")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["dpsAll"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置战斗力/神级为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "28" || ArrNow[0] == "防弹值")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["bulletDedut"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置防弹值为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "29" || ArrNow[0] == "每日扫荡次数")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["sweepingNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置每日扫荡次数为：" + ArrNow[1] + "次");
            }
            // 经验和掉率相关属性
            else if(ArrNow[0] == "30" || ArrNow[0] == "经验获取")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["exp"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置经验获取为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "31" || ArrNow[0] == "经验获取VIP")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["expVip"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置VIP经验获取为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "32" || ArrNow[0] == "武器碎片掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["weaponDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置武器碎片掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "33" || ArrNow[0] == "装备碎片掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["blackEquipDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置装备碎片掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "34" || ArrNow[0] == "随机武器掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["ranBlackArmsDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置随机武器掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "35" || ArrNow[0] == "稀有装备掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["rareEquipDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置稀有装备掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "36" || ArrNow[0] == "特殊零件掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["specialPartsDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置特殊零件掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "37" || ArrNow[0] == "宝石掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["gemDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置宝石掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "38" || ArrNow[0] == "尸宠图鉴掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["petBookDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置尸宠图鉴掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "39" || ArrNow[0] == "红橙基因体掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["rareGeneDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置红橙基因体掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "40" || ArrNow[0] == "副手掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["blackArmsDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置副手掉率为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "41" || ArrNow[0] == "银币获取")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["coinMul"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置银币获取为：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "42" || ArrNow[0] == "稀有武器掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["rareArmsDropPro"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置稀有武器掉率为：" + ArrNow[1] + "%");
            }
         }
         this.nowData.save = s0;

         // 强制刷新装备数据和显示
         this.nowData.save.fleshSMaxLv();
         if(this.nowData.placeType == "wear")
         {
            this.nowData.normalPlayerData.fleshAllByEquip();
         }

         // 重新计算属性数据
         var nowProDataArr2:Array = EquipSkillCreator.getOneProDataArr(this.nowData.save);
         OneProData.setLockByOther(nowProDataArr2,this.nowProDataArr);
         this.nowProDataArr = nowProDataArr2;

         // 调试信息：显示obj内容
         if(s0.obj != null)
         {
            var debugInfo:String = "装备obj属性: ";
            for(var prop:String in s0.obj)
            {
               debugInfo += prop + "=" + s0.obj[prop] + " ";
            }
            Gaming.uiGroup.alertBox.showSuccess(debugInfo);
         }

         // 强制重新初始化属性创建器
         try {
            Gaming.defineGroup.equipPropertyDataCreator.inAllProArr();
         } catch(e:Error) {
            // 忽略错误
         }

         // 刷新显示
         this.showOneEquipDataAndPan(this.nowData);
         Gaming.uiGroup.allBagUI.fleshAllBox();

         Gaming.uiGroup.alertBox.showSuccess("装备属性编辑成功！");
      }
      
      private function getAllEquipSkills(partType:String) : Array
      {
         var allSkills:Array = [];

         // 根据装备类型获取对应的技能
         if(partType == "head" || partType == "belt")
         {
            // 头盔和腰带技能 (headSkill)
            allSkills = [
               "godHand_equip",           // 上帝的护佑
               "immune_equip",            // 免疫
               "magneticField_equip",     // 磁力场
               "strongHalo_equip",        // 顽强光环
               "murderous_equip",         // 嗜爪之怒
               "poisonRange_equip",       // 瘴气
               "attackSpeedHalo_equip"    // 耐久光环
            ];
         }
         else if(partType == "fashion")
         {
            // 时装技能 (fashionSkill)
            allSkills = [
               "summonWolf_bigBoss",      // 召唤群狼
               "zoomOut"                  // 装甲压制
            ];
         }
         else
         {
            // 战衣和裤子技能 (coatSkill)
            allSkills = [
               "sacrifice_equip",         // 牺牲
               "backStrong_equip",        // 钢背
               "anionSkin_equip",         // 负离子外壳
               "treater_equip",           // 净化器
               "backWeak_equip",          // 芒刺
               "thornSkin_equip",         // 荆棘外表
               "refraction_equip"         // 折射
            ];
         }

         return allSkills;
      }

      // 显示装备编辑分页菜单
      private function showEquipEditMenu() : void
      {
         var menuText:String = "装备编辑菜单\n\n";
         menuText += "【1】基础属性编辑\n";
         menuText += "【2】掉落物品编辑\n";
         menuText += "【3】掉率属性编辑\n";
         menuText += "【4】经验好感编辑\n";
         menuText += "【5】战斗属性编辑\n";
         menuText += "【6】技能管理编辑\n";
         menuText += "【7】快速设置模板\n\n";
         menuText += "请输入页面编号(1-7)：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "", this.onEditMenuSelect);
      }

      // 处理编辑菜单选择
      private function onEditMenuSelect(selection:String) : void
      {
         var pageNum:int = parseInt(selection);

         switch(pageNum)
         {
            case 1:
               this.showBasicEditPage();
               break;
            case 2:
               this.showDropItemsEditPage();
               break;
            case 3:
               this.showDropRateEditPage();
               break;
            case 4:
               this.showExpLoveEditPage();
               break;
            case 5:
               this.showCombatEditPage();
               break;
            case 6:
               this.showSkillEditPage();
               break;
            case 7:
               this.showQuickTemplatesPage();
               break;
            default:
               Gaming.uiGroup.alertBox.showError("无效的页面编号！请输入1-7之间的数字。");
               break;
         }
      }

      // 页面1：基础属性编辑
      private function showBasicEditPage() : void
      {
         var helpText:String = "基础属性编辑\n\n";
         helpText += "等级[00*数值]        强化[02*数值]\n";
         helpText += "颜色[01*颜色]        进化[03*数值]\n";
         helpText += "类型[04*类型]        基础[05*数值]\n";
         helpText += "获取时间[06*日期]    到期[07*日期]\n";
         helpText += "永不过期[08]\n\n";
         helpText += "颜色选项: 白绿蓝紫橙红黑暗金紫金氩星\n";
         helpText += "类型选项: 衣服裤子头盔腰带时装\n";
         helpText += "日期格式: 2025-05-20 12:00:00\n\n";
         helpText += "示例: 00*99&01*红&02*20&03*5\n\n";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面2：掉落物品编辑
      private function showDropItemsEditPage() : void
      {
         var helpText:String = "掉落物品编辑\n\n";
         helpText += "无双水晶[12*数量]    万能球[13*数量]\n";
         helpText += "战神之心[14*数量]    幸运值[15*数值]\n";
         helpText += "优胜券[17*数量]      载具片[18*数量]\n";
         helpText += "扫荡次数[29*次数]\n\n";
         helpText += "推荐模板:\n";
         helpText += "神级掉落: 12*999&13*999&14*999&15*999\n";
         helpText += "日常增强: 17*999&18*999&29*999\n\n";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面3：掉率属性编辑
      private function showDropRateEditPage() : void
      {
         var helpText:String = "掉率属性编辑\n\n";
         helpText += "生命催化剂[19*%]     神能石[20*%]\n";
         helpText += "转化石[21*%]         化石[22*%]\n";
         helpText += "血手[23*%]           装置[24*%]\n";
         helpText += "宝石[37*%]           尸宠图[38*%]\n";
         helpText += "红橙基因[39*%]\n\n";
         helpText += "超级掉率模板:\n";
         helpText += "19*99900&20*99900&21*99900&22*99900&23*99900&24*99900&37*99900&38*99900&39*99900\n\n";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面4：经验好感编辑
      private function showExpLoveEditPage() : void
      {
         var helpText:String = "经验好感编辑\n\n";
         helpText += "经验获取[30*%]       VIP经验[31*%]\n";
         helpText += "赠礼好感[25*点]      每日好感[26*点]\n";
         helpText += "商运掉率[16*%]       银币获取[41*%]\n\n";
         helpText += "推荐模板:\n";
         helpText += "经验狂魔: 30*999&31*999\n";
         helpText += "好感达人: 25*999&26*999\n";
         helpText += "财富之王: 16*99900&41*99900\n";
         helpText += "全能套装: 30*999&31*999&25*999&26*999&16*99900\n\n";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面5：战斗属性编辑
      private function showCombatEditPage() : void
      {
         var helpText:String = "战斗属性编辑\n\n";
         helpText += "战斗力神级[27*%]     防弹值[28*%]\n";
         helpText += "武器碎片[32*%]       装备碎片[33*%]\n";
         helpText += "随机武器[34*%]       稀有装备[35*%]\n";
         helpText += "特殊零件[36*%]       副手[40*%]\n";
         helpText += "稀有武器[42*%]\n\n";
         helpText += "战斗模板:\n";
         helpText += "攻防一体: 27*15&28*39\n";
         helpText += "装备收集: 32*99900&33*99900&34*99900&35*99900&36*99900&40*99900&42*99900\n\n";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面6：技能管理编辑
      private function showSkillEditPage() : void
      {
         var helpText:String = "技能管理编辑\n\n";
         helpText += "技能添加[09*代码]    技能删除[10*代码]\n";
         helpText += "添加全部[09*所有]    清除数据[11]\n\n";
         helpText += "常用技能代码:\n";
         helpText += "头盔/腰带:\n";
         helpText += "  godHand_equip\n";
         helpText += "  immune_equip\n";
         helpText += "  magneticField_equip\n";
         helpText += "战衣/裤子:\n";
         helpText += "  sacrifice_equip\n";
         helpText += "  backStrong_equip\n";
         helpText += "  anionSkin_equip\n";
         helpText += "时装:\n";
         helpText += "  summonWolf_bigBoss\n";
         helpText += "  zoomOut\n\n";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面7：快速设置模板
      private function showQuickTemplatesPage() : void
      {
         var helpText:String = "快速设置模板\n\n";
         helpText += "【A】神级全能装备\n";
         helpText += "【B】掉率收集装备\n";
         helpText += "【C】经验成长装备\n";
         helpText += "【D】战斗强化装备\n";
         helpText += "【E】日常便利装备\n";
         helpText += "【F】财富积累装备\n";
         helpText += "【G】好感度装备\n\n";
         helpText += "请输入模板字母(A-G)或自定义指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.onTemplateSelect);
      }

      // 处理模板选择
      private function onTemplateSelect(selection:String) : void
      {
         var template:String = "";

         switch(selection.toUpperCase())
         {
            case "A":
               // 神级全能装备
               template = "00*99&01*氩星&02*99&03*9&12*999&13*999&14*999&15*999&16*99900&17*999&18*999&19*99900&20*99900&21*99900&22*99900&23*99900&24*99900&25*999&26*999&27*15&28*39&29*999&30*999&31*999&37*99900&38*99900&39*99900&09*所有";
               Gaming.uiGroup.alertBox.showSuccess("已应用神级全能装备模板！");
               break;
            case "B":
               // 掉率收集装备
               template = "19*99900&20*99900&21*99900&22*99900&23*99900&24*99900&32*99900&33*99900&34*99900&35*99900&36*99900&37*99900&38*99900&39*99900&40*99900&42*99900";
               Gaming.uiGroup.alertBox.showSuccess("已应用掉率收集装备模板！");
               break;
            case "C":
               // 经验成长装备
               template = "30*999&31*999&15*999&00*99&01*氩星&02*50";
               Gaming.uiGroup.alertBox.showSuccess("已应用经验成长装备模板！");
               break;
            case "D":
               // 战斗强化装备
               template = "27*15&28*39&32*99900&33*99900&34*99900&35*99900&36*99900&40*99900&42*99900&09*所有";
               Gaming.uiGroup.alertBox.showSuccess("已应用战斗强化装备模板！");
               break;
            case "E":
               // 日常便利装备
               template = "12*999&13*999&14*999&17*999&18*999&29*999&16*99900&41*99900";
               Gaming.uiGroup.alertBox.showSuccess("已应用日常便利装备模板！");
               break;
            case "F":
               // 财富积累装备
               template = "16*99900&41*99900&15*999&30*500&31*500";
               Gaming.uiGroup.alertBox.showSuccess("已应用财富积累装备模板！");
               break;
            case "G":
               // 好感度装备
               template = "25*999&26*999&15*999&30*300&31*300";
               Gaming.uiGroup.alertBox.showSuccess("已应用好感度装备模板！");
               break;
            default:
               // 自定义指令
               template = selection;
               Gaming.uiGroup.alertBox.showSuccess("正在执行自定义指令...");
               break;
         }

         if(template != "")
         {
            this.EquipEdit_Equip(template);
         }
      }

      private function affterGotoBack() : void
      {
         this.remakeBySave(this.beforeSave);
         this.beforeSave = null;
         Gaming.soundGroup.playSound("uiSound","getItems");
      }
      
      private function affterGetStoreState(v0:int) : void
      {
         var must_d0:MustDefine = null;
         if(v0 == 1 || v0 == -2)
         {
            Gaming.uiGroup.connectUI.hide();
            must_d0 = EquipRemakeCtrl.getMust(this.nowData.save,this.nowProDataArr);
            PlayerMustCtrl.deductMust(must_d0,this.afterRemake);
         }
      }
      
      private function remakeBySave(s0:EquipSave) : void
      {
         EquipRemakeCtrl.setAllByOther(this.nowData.save,s0);
         if(this.nowData.placeType == "wear")
         {
            this.nowData.normalPlayerData.fleshAllByEquip();
         }
         var nowProDataArr2:Array = EquipSkillCreator.getOneProDataArr(this.nowData.save);
         OneProData.setLockByOther(nowProDataArr2,this.nowProDataArr);
         this.nowProDataArr = nowProDataArr2;
         this.showOneEquipDataAndPan(this.nowData,false);
         Gaming.uiGroup.allBagUI.fleshAllBox();
      }
      
      private function afterRemake() : void
      {
         this.beforeSave = new EquipSave();
         this.beforeSave.inData_byObj(this.nowData.save);
         this.remakeBySave(this.tempRemakeSave);
         ++Gaming.PG.save.headCount.equipRemakeNum;
         UIOrder.save(true,false,false,null,null,false,true);
      }
   }
}

